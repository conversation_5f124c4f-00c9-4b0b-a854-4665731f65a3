import { createResource, createSignal, For, Show } from 'solid-js';
import { useNavigate } from '@solidjs/router';
import { productBaseApi } from './api';
import type { ProductBase } from './types';
import { PencilIcon, TrashIcon, PlusIcon, BuildingStorefrontIcon } from '../../assets/Icons.jsx';

interface Props {}

export default function ProductBaseList(props: Props) {
  const navigate = useNavigate();
  const [page, setPage] = createSignal(1);
  const [pageSize] = createSignal(10);
  const [editing, setEditing] = createSignal<ProductBase | null>(null);
  const [showForm, setShowForm] = createSignal(false);
  const [form, setForm] = createSignal<Partial<ProductBase>>({});

  const [data, { refetch }] = createResource(
    () => [page(), pageSize()] as [number, number],
    ([page, pageSize]) => productBaseApi.list(page, pageSize),
    { initialValue: { list: [], pagination: { page: 1, pageSize: 10, total: 0 } } }
  );

  function handleEdit(product: ProductBase) {
    setEditing(product);
    setForm({ ...product });
    setShowForm(true);
  }

  function handleAdd() {
    setEditing(null);
    setForm({});
    setShowForm(true);
  }

  async function handleSubmit(e: Event) {
    e.preventDefault();
    try {
      if (editing()) {
        await productBaseApi.update(editing()!.id, form() as any);
      } else {
        await productBaseApi.add(form() as any);
      }
      setShowForm(false);
      refetch();
    } catch (error) {
      alert('操作失败：' + (error as Error).message);
    }
  }

  async function handleDelete(id: string) {
    if (window.confirm('确定删除该商品？删除后相关的平台模板也会被删除。')) {
      try {
        await productBaseApi.remove(id);
        refetch();

      } catch (error) {
        alert('删除失败：' + (error as Error).message);
      }
    }
  }



  function handlePlatformTemplate(product: ProductBase) {
    navigate(`/product-management/platform-template/${product.id}`);
  }

  return (
    <div class="min-h-[70vh] flex flex-col">
      <div class="flex justify-between items-center mb-4">
        <span class="font-bold text-lg">商品底版信息</span>
        <button class="btn btn-primary btn-sm" onClick={handleAdd}>
          <PlusIcon />
          新增商品
        </button>
      </div>

      <Show when={data()} fallback={<div class="text-center py-8">加载中...</div>}>
        <div class="overflow-x-auto flex-1">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th class="text-left">商品名称</th>
                <th class="text-left">商品分类</th>
                <th class="text-left">商品品类</th>
                <th class="text-left">供应商</th>
                <th class="text-left">创建时间</th>
                <th class="text-center">操作</th>
              </tr>
            </thead>
            <tbody>
              <For each={data()!.list}>
                {(product) => (
                  <tr class="hover:bg-base-200">
                    <td class="text-left font-medium">{product.name}</td>
                    <td class="text-left">{product.category}</td>
                    <td class="text-left">{product.type}</td>
                    <td class="text-left">{product.supplier}</td>
                    <td class="text-left">{product.createdAt}</td>
                    <td class="text-center">
                      <div class="flex justify-center gap-2">
                        <button
                          class="btn btn-outline btn-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(product);
                          }}
                        >
                          <PencilIcon />
                          编辑
                        </button>
                        <button
                          class="btn btn-outline btn-primary btn-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePlatformTemplate(product);
                          }}
                        >
                          <BuildingStorefrontIcon />
                          平台模板
                        </button>
                        <button
                          class="btn btn-outline btn-error btn-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(product.id);
                          }}
                        >
                          <TrashIcon />
                          删除
                        </button>
                      </div>
                    </td>
                  </tr>
                )}
              </For>
            </tbody>
          </table>

          <Show when={data()!.list.length === 0}>
            <div class="text-center py-12 text-base-content/70">
              <div class="text-lg mb-2">暂无商品数据</div>
              <div class="text-sm">点击"新增商品"按钮添加第一个商品</div>
            </div>
          </Show>
        </div>

        {/* 分页 */}
        <Show when={data()!.pagination.total > data()!.pagination.pageSize}>
          <div class="flex justify-center mt-4">
            <div class="join">
              <button 
                class="join-item btn btn-sm"
                disabled={page() <= 1}
                onClick={() => setPage(page() - 1)}
              >
                上一页
              </button>
              <button class="join-item btn btn-sm btn-active">
                {page()} / {Math.ceil(data()!.pagination.total / data()!.pagination.pageSize)}
              </button>
              <button 
                class="join-item btn btn-sm"
                disabled={page() >= Math.ceil(data()!.pagination.total / data()!.pagination.pageSize)}
                onClick={() => setPage(page() + 1)}
              >
                下一页
              </button>
            </div>
          </div>
        </Show>
      </Show>

      {/* 新增/编辑弹窗 */}
      <Show when={showForm()}>
        <dialog class="modal modal-open">
          <div class="modal-box">
            <h3 class="font-bold text-lg mb-4">
              {editing() ? '编辑商品' : '新增商品'}
            </h3>
            <form onSubmit={handleSubmit}>
              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text">商品名称 *</span>
                </label>
                <input 
                  type="text"
                  class="input input-bordered w-full" 
                  value={form().name || ''}
                  onInput={e => setForm(f => ({ ...f, name: e.currentTarget.value }))}
                  required
                />
              </div>
              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text">商品分类 *</span>
                </label>
                <input 
                  type="text"
                  class="input input-bordered w-full" 
                  value={form().category || ''}
                  onInput={e => setForm(f => ({ ...f, category: e.currentTarget.value }))}
                  required
                />
              </div>
              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text">商品品类 *</span>
                </label>
                <input 
                  type="text"
                  class="input input-bordered w-full" 
                  value={form().type || ''}
                  onInput={e => setForm(f => ({ ...f, type: e.currentTarget.value }))}
                  required
                />
              </div>
              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text">供应商 *</span>
                </label>
                <input 
                  type="text"
                  class="input input-bordered w-full" 
                  value={form().supplier || ''}
                  onInput={e => setForm(f => ({ ...f, supplier: e.currentTarget.value }))}
                  required
                />
              </div>
              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text">商品描述</span>
                </label>
                <textarea 
                  class="textarea textarea-bordered w-full" 
                  rows="3"
                  value={form().description || ''}
                  onInput={e => setForm(f => ({ ...f, description: e.currentTarget.value }))}
                />
              </div>
              <div class="modal-action">
                <button class="btn btn-primary" type="submit">保存</button>
                <button class="btn" type="button" onClick={() => setShowForm(false)}>取消</button>
              </div>
            </form>
          </div>
          <form method="dialog" class="modal-backdrop">
            <button onClick={() => setShowForm(false)}>关闭</button>
          </form>
        </dialog>
      </Show>
    </div>
  );
}
