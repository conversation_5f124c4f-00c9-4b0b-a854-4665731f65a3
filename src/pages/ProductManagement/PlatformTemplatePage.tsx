import { useParams, useNavigate } from '@solidjs/router';
import { createResource, createEffect, createSignal } from 'solid-js';
import { productBaseApi } from './api';
import PlatformTemplate from './PlatformTemplate';
import type { ProductBase } from './types';
import styles from '../ConfigCenter/ConfigCenter.module.css';
import { ArrowLeftIcon } from '../../assets/Icons.jsx';

export default function PlatformTemplatePage() {
  const params = useParams();
  const navigate = useNavigate();
  const [selectedProduct, setSelectedProduct] = createSignal<ProductBase | null>(null);

  // 根据URL参数加载商品信息
  const [product] = createResource(
    () => params.productId,
    async (productId) => {
      if (!productId) return null;
      try {
        return await productBaseApi.getById(productId);
      } catch (error) {
        console.error('Failed to load product:', error);
        return null;
      }
    }
  );

  // 当商品数据加载完成时，设置选中的商品
  createEffect(() => {
    const productData = product();
    if (productData) {
      setSelectedProduct(productData);
    }
  });

  // 如果没有商品ID或商品加载失败，显示错误信息
  if (!params.productId) {
    return (
      <div class={styles['config-center-bg']} style={{ 'min-height': '100vh', padding: '32px 0' }}>
        <div style={{ 'max-width': '95%', margin: '0 auto', padding: '0 32px' }}>
          <div class={styles['config-card']}>
            <div class="text-center py-12">
              <h2 class="text-xl font-bold mb-4">缺少商品信息</h2>
              <p class="text-base-content/70 mb-6">请先选择一个商品再访问平台模板页面</p>
              <button 
                class="btn btn-primary"
                onClick={() => navigate('/product-management')}
              >
                返回商品管理
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div class={styles['config-center-bg']} style={{ 'min-height': '100vh', padding: '32px 0' }}>
      <div style={{ 'max-width': '95%', margin: '0 auto', padding: '0 32px' }}>
        {/* 页面头部 */}
        <div class="flex items-center gap-4 mb-6">
          <button
            class="btn btn-ghost btn-circle"
            onClick={() => navigate('/product-management')}
          >
            <ArrowLeftIcon />
          </button>
          <div class="flex-1">
            <h1 class="text-2xl font-bold text-base-content">平台样品模板</h1>
            {selectedProduct() && (
              <p class="text-base-content/70 mt-1">
                商品：{selectedProduct()!.name} ({selectedProduct()!.category})
              </p>
            )}
          </div>
        </div>

        <div class={styles['config-card']}>
          {selectedProduct() ? (
            <PlatformTemplate 
              selectedProduct={selectedProduct()}
              onProductSelect={setSelectedProduct}
            />
          ) : (
            <div class="text-center py-12">
              <span class="loading loading-spinner loading-lg text-primary"></span>
              <p class="text-xl text-primary/70 mt-4">正在加载商品信息...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
