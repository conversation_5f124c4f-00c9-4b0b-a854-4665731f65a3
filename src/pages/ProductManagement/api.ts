import type { ProductBase, TemuTemplate, PageResponse } from './types';

const BASE_URL = '/api/v1/product';

// Mock数据 - 商品底版信息
let mockProducts: ProductBase[] = [
  {
    id: '1',
    name: '基础款T恤',
    category: '上装',
    type: 'T恤',
    supplier: '供应商A',
    description: '经典基础款T恤，适合日常穿着',
    createdAt: '2024-06-01'
  },
  {
    id: '2', 
    name: '休闲牛仔裤',
    category: '下装',
    type: '牛仔裤',
    supplier: '供应商B',
    description: '舒适休闲牛仔裤',
    createdAt: '2024-06-02'
  }
];

// Mock数据 - Temu模板
let mockTemuTemplates: TemuTemplate[] = [
  {
    id: '1',
    productId: '1',
    publishTitle: '基础款纯棉T恤 舒适透气 多色可选',
    shopNumber: 'SHOP001',
    skc: 'SKC001',
    origin: '中国',
    province: '广东省',
    currency: 'CNY',
    specifications: [
      { id: '1', platformSize: 'S', declaredPrice: 25.00, weight: 150 },
      { id: '2', platformSize: 'M', declaredPrice: 25.00, weight: 160 },
      { id: '3', platformSize: 'L', declaredPrice: 25.00, weight: 170 }
    ],
    sizeChart: {
      id: '1',
      selectedParameters: ['length', 'chest', 'sleeve'],
      selectedSpecs: ['S', 'M', 'L'],
      measurements: [
        {
          id: '1',
          sizeSpec: 'S',
          measurements: { length: 65, chest: 96, sleeve: 20 }
        },
        {
          id: '2',
          sizeSpec: 'M',
          measurements: { length: 67, chest: 100, sleeve: 21 }
        },
        {
          id: '3',
          sizeSpec: 'L',
          measurements: { length: 69, chest: 104, sleeve: 22 }
        }
      ]
    },
    createdAt: '2024-06-01'
  }
];

// 商品底版信息API
export const productBaseApi = {
  // 分页查询商品
  list: async (page: number = 1, pageSize: number = 10): Promise<PageResponse<ProductBase>> => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const list = mockProducts.slice(start, end);
    
    return {
      list,
      pagination: {
        page,
        pageSize,
        total: mockProducts.length
      }
    };
  },

  // 新增商品
  add: async (data: Omit<ProductBase, 'id' | 'createdAt'>): Promise<ProductBase> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const newProduct: ProductBase = {
      ...data,
      id: Date.now().toString(),
      createdAt: new Date().toISOString().split('T')[0]
    };
    
    mockProducts.unshift(newProduct);
    return newProduct;
  },

  // 更新商品
  update: async (id: string, data: Partial<ProductBase>): Promise<ProductBase> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const index = mockProducts.findIndex(p => p.id === id);
    if (index === -1) throw new Error('商品不存在');
    
    mockProducts[index] = {
      ...mockProducts[index],
      ...data,
      updatedAt: new Date().toISOString().split('T')[0]
    };
    
    return mockProducts[index];
  },

  // 根据ID获取商品
  getById: async (id: string): Promise<ProductBase | null> => {
    await new Promise(resolve => setTimeout(resolve, 300));

    return mockProducts.find(p => p.id === id) || null;
  },

  // 删除商品
  remove: async (id: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 300));

    const index = mockProducts.findIndex(p => p.id === id);
    if (index === -1) throw new Error('商品不存在');

    mockProducts.splice(index, 1);
    return true;
  }
};

// Temu模板API
export const temuTemplateApi = {
  // 根据商品ID获取模板
  getByProductId: async (productId: string): Promise<TemuTemplate | null> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return mockTemuTemplates.find(t => t.productId === productId) || null;
  },

  // 保存模板
  save: async (data: Omit<TemuTemplate, 'id' | 'createdAt'>): Promise<TemuTemplate> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 检查是否已存在该商品的模板
    const existingIndex = mockTemuTemplates.findIndex(t => t.productId === data.productId);
    
    if (existingIndex >= 0) {
      // 更新现有模板
      mockTemuTemplates[existingIndex] = {
        ...mockTemuTemplates[existingIndex],
        ...data,
        updatedAt: new Date().toISOString().split('T')[0]
      };
      return mockTemuTemplates[existingIndex];
    } else {
      // 创建新模板
      const newTemplate: TemuTemplate = {
        ...data,
        id: Date.now().toString(),
        createdAt: new Date().toISOString().split('T')[0]
      };
      mockTemuTemplates.push(newTemplate);
      return newTemplate;
    }
  },

  // 删除模板
  remove: async (productId: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const index = mockTemuTemplates.findIndex(t => t.productId === productId);
    if (index === -1) return false;
    
    mockTemuTemplates.splice(index, 1);
    return true;
  }
};
