import { createSignal, Show } from 'solid-js';
import type { ProductBase, Platform } from './types';
import TemuTemplate from './TemuTemplate';
import { BuildingStorefrontIcon } from '../../assets/Icons.jsx';

interface Props {
  selectedProduct: ProductBase | null;
  onProductSelect?: (product: ProductBase | null) => void;
}

const platforms: Platform[] = [
  {
    key: 'temu',
    label: 'Temu',
    description: 'Temu平台样品模板配置'
  }
  // 后续可以扩展其他平台
  // {
  //   key: 'amazon',
  //   label: 'Amazon',
  //   description: 'Amazon平台样品模板配置'
  // }
];

export default function PlatformTemplate(props: Props) {
  const [selectedPlatform, setSelectedPlatform] = createSignal('temu');

  return (
    <div class="space-y-6">
      {/* 商品选择提示 */}
      <Show when={!props.selectedProduct}>
        <div class="card bg-base-100 shadow">
          <div class="card-body text-center py-12">
            <BuildingStorefrontIcon class="w-16 h-16 mx-auto mb-4 text-base-content/50" />
            <h3 class="text-xl font-bold mb-2">选择商品配置平台模板</h3>
            <p class="text-base-content/70 mb-4">
              请先在左侧"底版信息"中选择一个商品，然后配置该商品在各平台的发布模板
            </p>
            <div class="text-sm text-base-content/60">
              平台模板包含商品在特定平台发布时需要的所有信息，如标题、规格、尺码表等
            </div>
          </div>
        </div>
      </Show>

      {/* 平台选择和模板配置 */}
      <Show when={props.selectedProduct}>
        <div class="space-y-6">
          {/* 平台选择 */}
          <div class="card bg-base-100 shadow">
            <div class="card-body">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h3 class="card-title">选择平台</h3>
                  <p class="text-base-content/70 mt-1">
                    商品：{props.selectedProduct?.name}
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {platforms.map(platform => (
                  <div 
                    class={`card cursor-pointer transition-all hover:shadow-lg ${
                      selectedPlatform() === platform.key 
                        ? 'border-2 border-primary bg-primary/5' 
                        : 'border border-base-300 hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedPlatform(platform.key)}
                  >
                    <div class="card-body p-4">
                      <div class="flex items-center gap-3">
                        <div class={`w-3 h-3 rounded-full ${
                          selectedPlatform() === platform.key ? 'bg-primary' : 'bg-base-300'
                        }`}></div>
                        <div class="flex-1">
                          <h4 class="font-bold">{platform.label}</h4>
                          <p class="text-sm text-base-content/70">{platform.description}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 根据选择的平台显示对应的模板组件 */}
          <Show when={selectedPlatform() === 'temu'}>
            <TemuTemplate product={props.selectedProduct!} />
          </Show>

          {/* 其他平台的模板组件可以在这里添加 */}
          {/* <Show when={selectedPlatform() === 'amazon'}>
            <AmazonTemplate product={props.selectedProduct!} />
          </Show> */}
        </div>
      </Show>
    </div>
  );
}
