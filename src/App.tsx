import { Router, Route } from '@solidjs/router';
import MainLayout from './layouts/MainLayout';
import ConfigCenter from './pages/ConfigCenter';
import ProductManagement from './pages/ProductManagement';
import PlatformTemplatePage from './pages/ProductManagement/PlatformTemplatePage';
import RawImageManager from './pages/RawImageManager';
import PatternExtraction from './pages/PatternExtraction';
import { ToastAlert } from './components/Alter';

export default function App() {
  return (
    <>
      <ToastAlert />
      <Router>
        <MainLayout>
          <Route path="/" component={RawImageManager} />
          <Route path="/raw-images" component={RawImageManager} />
          <Route path="/pattern-extraction" component={PatternExtraction} />
          <Route path="/product-management" component={ProductManagement} />
          <Route path="/product-management/platform-template/:productId" component={PlatformTemplatePage} />
          <Route path="/config-center" component={ConfigCenter} />
          {/* 其他路由可以在这里添加 */}
        </MainLayout>
      </Router>
    </>
  );
}
